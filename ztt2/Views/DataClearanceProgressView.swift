//
//  DataClearanceProgressView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 数据清除进度视图
 * 
 * 参照ztt1的AccountDeletionProgressView实现，提供实时进度显示：
 * - 显示当前清除步骤
 * - 实时进度条
 * - 支持取消操作
 * - 错误处理和重试
 */
struct DataClearanceProgressView: View {
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    @StateObject private var clearanceManager = DataClearanceManager.shared
    @State private var showingCancelAlert = false
    @State private var showingErrorAlert = false
    @State private var showingCompletionAlert = false
    @State private var hasStartedClearance = false
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                Spacer()
                
                progressSection
                statusSection
                
                Spacer()
                
                if clearanceManager.isClearingData {
                    cancelButtonSection
                } else if clearanceManager.clearanceError != nil {
                    retryButtonSection
                }
            }
            .padding(.horizontal, 32)
            .navigationTitle("清除数据")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
        }
        .onAppear {
            startClearanceIfNeeded()
        }
        .onChange(of: clearanceManager.isClearingData) { isClearingData in
            if !isClearingData && clearanceManager.clearanceError == nil && hasStartedClearance {
                // 清除完成
                showingCompletionAlert = true
            }
        }
        .onChange(of: clearanceManager.clearanceError) { error in
            if error != nil {
                showingErrorAlert = true
            }
        }
        .alert("取消清除", isPresented: $showingCancelAlert) {
            Button("继续清除", role: .cancel) { }
            Button("确认取消", role: .destructive) {
                clearanceManager.cancelClearance()
                dismiss()
            }
        } message: {
            Text("确定要取消数据清除操作吗？已清除的数据无法恢复。")
        }
        .alert("清除失败", isPresented: $showingErrorAlert) {
            Button("重试") {
                retryOperation()
            }
            Button("取消", role: .cancel) {
                dismiss()
            }
        } message: {
            Text(clearanceManager.clearanceError?.localizedDescription ?? "未知错误")
        }
        .alert("清除完成", isPresented: $showingCompletionAlert) {
            Button("确定") {
                exitApplication()
            }
        } message: {
            Text("所有数据已成功清除。应用将重新启动以完成重置。")
        }
    }
    
    // MARK: - View Components
    
    /**
     * 进度显示区域
     */
    private var progressSection: some View {
        VStack(spacing: 24) {
            // 进度环
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                    .frame(width: 120, height: 120)
                
                Circle()
                    .trim(from: 0, to: clearanceManager.clearanceProgress)
                    .stroke(
                        clearanceManager.clearanceError != nil ? Color.red : Color.blue,
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: clearanceManager.clearanceProgress)
                
                VStack {
                    if clearanceManager.isClearingData {
                        Image(systemName: "trash.fill")
                            .font(.title)
                            .foregroundColor(.blue)
                    } else if clearanceManager.clearanceError != nil {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.title)
                            .foregroundColor(.red)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.green)
                    }
                    
                    Text("\(Int(clearanceManager.clearanceProgress * 100))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }
            
            // 进度条
            ProgressView(value: clearanceManager.clearanceProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: clearanceManager.clearanceError != nil ? .red : .blue))
                .scaleEffect(y: 2)
        }
    }
    
    /**
     * 状态显示区域
     */
    private var statusSection: some View {
        VStack(spacing: 16) {
            if clearanceManager.isClearingData {
                Text("正在清除数据...")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            } else if clearanceManager.clearanceError != nil {
                Text("清除失败")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)
            } else if hasStartedClearance {
                Text("清除完成")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
            } else {
                Text("准备清除")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            if !clearanceManager.clearanceStatus.isEmpty {
                Text(clearanceManager.clearanceStatus)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)
            }
            
            if clearanceManager.isClearingData {
                Text("请勿关闭应用，此过程可能需要几分钟...")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)
            }
        }
    }
    
    /**
     * 取消按钮区域
     */
    private var cancelButtonSection: some View {
        Button("取消清除") {
            showingCancelAlert = true
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color.red.opacity(0.1))
        .foregroundColor(.red)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red, lineWidth: 1)
        )
    }
    
    /**
     * 重试按钮区域
     */
    private var retryButtonSection: some View {
        VStack(spacing: 12) {
            Button("重试") {
                retryOperation()
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
            
            Button("取消") {
                dismiss()
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color.gray.opacity(0.2))
            .foregroundColor(.primary)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Methods
    
    /**
     * 开始清除操作（如果需要）
     */
    private func startClearanceIfNeeded() {
        guard !hasStartedClearance else { return }
        
        hasStartedClearance = true
        
        clearanceManager.clearCurrentUserData { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    print("✅ 数据清除完成")
                case .failure(let error):
                    print("❌ 数据清除失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 重试操作
     */
    private func retryOperation() {
        // 重置错误状态
        clearanceManager.clearanceError = nil
        
        // 重新开始清除
        clearanceManager.clearCurrentUserData { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    print("✅ 重试成功，数据清除完成")
                case .failure(let error):
                    print("❌ 重试失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 退出应用
     */
    private func exitApplication() {
        // 发送应用退出通知
        NotificationCenter.default.post(name: .dataClearanceCompleted, object: nil)
        
        // 延迟退出，给通知处理时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            exit(0)
        }
    }
}
