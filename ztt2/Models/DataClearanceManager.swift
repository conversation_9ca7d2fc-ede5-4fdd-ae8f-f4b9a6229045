//
//  DataClearanceManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CloudKit
import CoreData
import Combine

/**
 * 数据清除管理器
 * 
 * 参照ztt1的AccountDeletionManager实现，提供完整的6步数据清除流程：
 * 1. 准备清除 - 数据统计、状态检查
 * 2. 创建清除标记 - CloudKit标记，防止数据恢复
 * 3. 删除本地数据 - Core Data本地清理
 * 4. 删除云端数据 - CloudKit私有数据库清理
 * 5. 清除认证信息 - 登录状态和敏感信息
 * 6. 最终清理 - 缓存、偏好设置、应用状态重置
 */
class DataClearanceManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataClearanceManager()
    
    // MARK: - Published Properties
    @Published var isClearingData = false
    @Published var clearanceProgress: Double = 0.0
    @Published var clearanceStatus: String = ""
    @Published var clearanceError: Error?
    
    // MARK: - Private Properties
    private let cloudKitContainer = CKContainer.default()
    private let coreDataManager = CoreDataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Data Models
    
    /**
     * 清除步骤枚举
     */
    enum ClearanceStep: Int, CaseIterable {
        case preparation = 0
        case createClearanceMark = 1
        case deleteLocalData = 2
        case deleteCloudData = 3
        case clearAuthentication = 4
        case finalCleanup = 5
        
        var description: String {
            switch self {
            case .preparation:
                return "data_clearance.step.preparation".localized
            case .createClearanceMark:
                return "data_clearance.step.create_mark".localized
            case .deleteLocalData:
                return "data_clearance.step.delete_local".localized
            case .deleteCloudData:
                return "data_clearance.step.delete_cloud".localized
            case .clearAuthentication:
                return "data_clearance.step.clear_auth".localized
            case .finalCleanup:
                return "data_clearance.step.final_cleanup".localized
            }
        }
    }
    
    /**
     * 用户数据信息结构
     */
    struct UserDataInfo {
        let appleUserID: String?
        let nickname: String?
        let email: String?
        let memberCount: Int
        let pointRecordCount: Int
        let diaryEntryCount: Int
        let subscriptionLevel: String?
        let createdAt: Date?
    }
    
    /**
     * 清除错误类型
     */
    enum ClearanceError: LocalizedError {
        case userNotFound
        case cloudKitNotAvailable
        case localDataClearanceFailed
        case cloudDataClearanceFailed
        case authenticationClearanceFailed
        case operationCancelled
        case unknownError(String)
        
        var errorDescription: String? {
            switch self {
            case .userNotFound:
                return "data_clearance.error.user_not_found".localized
            case .cloudKitNotAvailable:
                return "data_clearance.error.cloudkit_unavailable".localized
            case .localDataClearanceFailed:
                return "data_clearance.error.local_clearance_failed".localized
            case .cloudDataClearanceFailed:
                return "data_clearance.error.cloud_clearance_failed".localized
            case .authenticationClearanceFailed:
                return "data_clearance.error.auth_clearance_failed".localized
            case .operationCancelled:
                return "data_clearance.error.operation_cancelled".localized
            case .unknownError(let message):
                return message
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        setupClearanceNotifications()
    }
    
    // MARK: - Public Methods
    
    /**
     * 清除当前用户的所有数据
     * 
     * 这是主要的清除方法，会执行完整的6步清除流程
     * 
     * @param completion 完成回调，返回清除结果
     */
    func clearCurrentUserData(completion: @escaping (Result<Void, Error>) -> Void) {
        print("🗑️ 开始清除用户数据...")
        
        // 重置状态
        DispatchQueue.main.async {
            self.isClearingData = true
            self.clearanceProgress = 0.0
            self.clearanceError = nil
        }
        
        // 检查用户是否存在
        guard let currentUser = getCurrentUser() else {
            handleClearanceError(ClearanceError.userNotFound, completion: completion)
            return
        }
        
        // 检查CloudKit可用性
        checkCloudKitAvailability { [weak self] available in
            guard let self = self else { return }
            
            if !available {
                self.handleClearanceError(ClearanceError.cloudKitNotAvailable, completion: completion)
                return
            }
            
            // 开始清除流程
            self.executeClearanceSteps(for: currentUser, completion: completion)
        }
    }
    
    /**
     * 检查是否有远程清除标记
     */
    func checkForRemoteClearanceMark(completion: @escaping (Bool) -> Void) {
        guard let currentUser = getCurrentUser(),
              let appleUserID = currentUser.appleUserID else {
            completion(false)
            return
        }
        
        print("🔍 检查远程清除标记: \(appleUserID)")
        
        let predicate = NSPredicate(format: "appleUserID == %@", appleUserID)
        let query = CKQuery(recordType: "DataClearanceMark", predicate: predicate)
        
        let operation = CKQueryOperation(query: query)
        operation.resultsLimit = 1
        
        var records: [CKRecord] = []
        
        operation.recordMatchedBlock = { (recordID, recordResult) in
            switch recordResult {
            case .success(let record):
                records.append(record)
            case .failure(let error):
                print("❌ 查询清除标记失败: \(error)")
            }
        }
        
        operation.queryResultBlock = { [weak self] (operationResult) in
            switch operationResult {
            case .success:
                let hasClearanceMark = !records.isEmpty
                if hasClearanceMark {
                    print("⚠️ 检测到远程清除标记，准备执行本地清理...")
                    self?.handleRemoteClearance(for: currentUser)
                }
                completion(hasClearanceMark)
            case .failure(let error):
                print("❌ 检查清除标记失败: \(error)")
                completion(false)
            }
        }
        
        cloudKitContainer.publicCloudDatabase.add(operation)
    }
    
    /**
     * 取消清除操作
     */
    func cancelClearance() {
        print("🔄 用户取消清除操作")
        
        DispatchQueue.main.async {
            self.isClearingData = false
            self.clearanceProgress = 0.0
            self.clearanceStatus = ""
            self.clearanceError = nil
        }
    }
    
    /**
     * 获取用户数据统计
     */
    func getUserDataStatistics() -> [String: Int] {
        guard let currentUser = getCurrentUser() else {
            return [:]
        }
        
        let context = coreDataManager.viewContext
        var stats: [String: Int] = [:]
        
        // 统计家庭成员数量
        let memberCount = currentUser.members?.count ?? 0
        stats["members"] = memberCount
        
        // 统计积分记录数量
        let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        pointRecordRequest.predicate = NSPredicate(format: "member.user == %@", currentUser)
        let pointRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0
        stats["pointRecords"] = pointRecordCount
        
        // 统计成长日记数量
        let diaryRequest: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
        diaryRequest.predicate = NSPredicate(format: "member.user == %@", currentUser)
        let diaryCount = (try? context.count(for: diaryRequest)) ?? 0
        stats["diaryEntries"] = diaryCount
        
        // 统计AI报告数量
        let aiReportRequest: NSFetchRequest<AIReport> = AIReport.fetchRequest()
        aiReportRequest.predicate = NSPredicate(format: "member.user == %@", currentUser)
        let aiReportCount = (try? context.count(for: aiReportRequest)) ?? 0
        stats["aiReports"] = aiReportCount
        
        return stats
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置清除通知
     */
    private func setupClearanceNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleApplicationWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
    }
    
    /**
     * 处理应用即将终止
     */
    @objc private func handleApplicationWillTerminate() {
        if isClearingData {
            print("⚠️ 应用即将终止，清除操作可能不完整")
            // 在这里可以保存清除状态，供下次启动时恢复
        }
    }
    
    /**
     * 检查CloudKit可用性
     */
    private func checkCloudKitAvailability(completion: @escaping (Bool) -> Void) {
        cloudKitContainer.accountStatus { status, error in
            DispatchQueue.main.async {
                let available = (status == .available)
                if let error = error {
                    print("❌ CloudKit状态检查失败: \(error)")
                }
                completion(available)
            }
        }
    }
    
    /**
     * 获取当前用户
     */
    private func getCurrentUser() -> User? {
        return coreDataManager.getCurrentUser()
    }
    
    /**
     * 处理清除错误
     */
    private func handleClearanceError(_ error: Error, completion: @escaping (Result<Void, Error>) -> Void) {
        DispatchQueue.main.async {
            self.isClearingData = false
            self.clearanceError = error
            completion(.failure(error))
        }
    }

    /**
     * 执行清除步骤
     */
    private func executeClearanceSteps(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
        let userInfo = UserDataInfo(
            appleUserID: user.appleUserID,
            nickname: user.nickname,
            email: user.email,
            memberCount: user.members?.count ?? 0,
            pointRecordCount: 0, // 将在准备步骤中计算
            diaryEntryCount: 0,  // 将在准备步骤中计算
            subscriptionLevel: user.subscription?.level,
            createdAt: user.createdAt
        )

        let steps = ClearanceStep.allCases
        let totalSteps = Double(steps.count)

        func executeStep(_ stepIndex: Int) {
            guard stepIndex < steps.count else {
                // 所有步骤完成
                self.completeClearance(completion: completion)
                return
            }

            let step = steps[stepIndex]
            let progress = Double(stepIndex) / totalSteps

            DispatchQueue.main.async {
                self.clearanceProgress = progress
                self.clearanceStatus = step.description
            }

            print("📋 执行清除步骤 \(stepIndex + 1)/\(steps.count): \(step.description)")

            // 执行当前步骤
            // 注意：步骤3删除本地数据后，user对象将被删除，后续步骤只能使用userInfo
            let currentUser = (stepIndex < 3) ? user : nil
            executeIndividualStep(step, user: currentUser, userInfo: userInfo) { [weak self] result in
                switch result {
                case .success:
                    // 继续下一步
                    executeStep(stepIndex + 1)
                case .failure(let error):
                    // 错误处理
                    self?.handleClearanceError(error, completion: completion)
                }
            }
        }

        executeStep(0)
    }

    /**
     * 执行单个清除步骤
     */
    private func executeIndividualStep(_ step: ClearanceStep, user: User?, userInfo: UserDataInfo, completion: @escaping (Result<Void, Error>) -> Void) {
        switch step {
        case .preparation:
            prepareClearance(for: user!, completion: completion)
        case .createClearanceMark:
            createClearanceMark(userInfo: userInfo, completion: completion)
        case .deleteLocalData:
            deleteLocalData(for: user!, completion: completion)
        case .deleteCloudData:
            deleteCloudData(userInfo: userInfo, completion: completion)
        case .clearAuthentication:
            clearAuthentication(userInfo: userInfo, completion: completion)
        case .finalCleanup:
            performFinalCleanup(userInfo: userInfo, completion: completion)
        }
    }

    /**
     * 完成清除
     */
    private func completeClearance(completion: @escaping (Result<Void, Error>) -> Void) {
        DispatchQueue.main.async {
            self.isClearingData = false
            self.clearanceProgress = 1.0
            self.clearanceStatus = "data_clearance.completed".localized
            completion(.success(()))
        }
    }

    /**
     * 处理远程清除
     */
    private func handleRemoteClearance(for user: User) {
        print("🔄 处理远程清除...")

        // 执行本地清理
        deleteLocalData(for: user) { result in
            switch result {
            case .success:
                print("✅ 远程清除处理完成")
                // 清理清除标记，避免重复弹窗
                self.cleanupClearanceMarks(for: user.appleUserID ?? "") { _ in
                    self.resetApplicationState()
                }
            case .failure(let error):
                print("❌ 远程清除处理失败: \(error)")
            }
        }
    }

    /**
     * 清理清除标记
     */
    func cleanupClearanceMarks(for appleUserID: String, completion: @escaping (Bool) -> Void) {
        guard !appleUserID.isEmpty else {
            completion(false)
            return
        }

        print("🧹 清理清除标记: \(appleUserID)")

        // 查询CloudKit中的清除标记
        let predicate = NSPredicate(format: "appleUserID == %@", appleUserID)
        let query = CKQuery(recordType: "DataClearanceMark", predicate: predicate)

        let operation = CKQueryOperation(query: query)
        operation.resultsLimit = CKQueryOperation.maximumResults

        var recordsToDelete: [CKRecord.ID] = []

        operation.recordMatchedBlock = { (recordID, recordResult) in
            switch recordResult {
            case .success(let record):
                recordsToDelete.append(record.recordID)
            case .failure(let error):
                print("❌ 查询清除标记失败: \(error)")
            }
        }

        operation.queryResultBlock = { [weak self] (operationResult) in
            switch operationResult {
            case .success:
                if !recordsToDelete.isEmpty {
                    // 删除找到的标记
                    self?.deleteRecords(recordsToDelete, completion: completion)
                } else {
                    print("ℹ️ 未找到需要清理的清除标记")
                    completion(true)
                }
            case .failure(let error):
                print("❌ 查询清除标记操作失败: \(error)")
                completion(false)
            }
        }

        cloudKitContainer.publicCloudDatabase.add(operation)
    }

    /**
     * 删除CloudKit记录
     */
    private func deleteRecords(_ recordIDs: [CKRecord.ID], completion: @escaping (Bool) -> Void) {
        Task {
            do {
                let (_, deleteResults) = try await cloudKitContainer.publicCloudDatabase.modifyRecords(saving: [], deleting: recordIDs)

                var successCount = 0
                for (recordID, result) in deleteResults {
                    switch result {
                    case .success():
                        successCount += 1
                    case .failure(let error):
                        print("❌ 删除记录 \(recordID) 失败: \(error)")
                    }
                }

                print("✅ 清除标记清理成功，共清理 \(successCount) 条记录")
                completion(successCount > 0)
            } catch {
                print("❌ 清除标记清理失败: \(error)")
                completion(false)
            }
        }
    }

    /**
     * 重置应用状态
     */
    private func resetApplicationState() {
        DispatchQueue.main.async {
            // 发送清除完成通知
            NotificationCenter.default.post(name: .dataClearanceCompleted, object: nil)
        }
    }

    // MARK: - Clearance Steps Implementation

    /**
     * 步骤1: 准备清除
     */
    private func prepareClearance(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🔍 步骤1: 准备清除用户数据...")

        // 检查数据完整性
        let dataStats = getUserDataStatistics()
        print("📊 用户数据统计: \(dataStats)")

        // 检查是否有活跃的订阅
        if let subscription = user.subscription,
           let level = subscription.level,
           level != "free" {
            print("⚠️ 检测到活跃订阅: \(level)")
            // 在实际应用中，这里应该提示用户取消订阅
        }

        // 准备完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            completion(.success(()))
        }
    }

    /**
     * 步骤2: 创建清除标记
     */
    private func createClearanceMark(userInfo: UserDataInfo, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🏷️ 步骤2: 创建清除标记...")

        guard let appleUserID = userInfo.appleUserID else {
            print("⚠️ 无法获取Apple用户ID，跳过清除标记创建")
            completion(.success(()))
            return
        }

        // 创建CloudKit清除标记
        let clearanceMarkRecord = CKRecord(recordType: "DataClearanceMark")
        clearanceMarkRecord["appleUserID"] = appleUserID
        clearanceMarkRecord["clearanceTimestamp"] = Date()
        clearanceMarkRecord["deviceInfo"] = "\(UIDevice.current.name) - \(UIDevice.current.systemVersion)"

        cloudKitContainer.publicCloudDatabase.save(clearanceMarkRecord) { record, error in
            if let error = error {
                print("❌ 创建清除标记失败: \(error)")
                completion(.failure(ClearanceError.cloudDataClearanceFailed))
                return
            }

            print("✅ 清除标记创建成功")
            completion(.success(()))
        }
    }

    /**
     * 步骤3: 删除本地数据
     */
    private func deleteLocalData(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🗄️ 步骤3: 删除本地数据...")

        let context = coreDataManager.viewContext

        // 需要清除的实体类型
        let entityNames = [
            "User",
            "Subscription",
            "GlobalRule",
            "Member",
            "PointRecord",
            "DiaryEntry",
            "AIReport",
            "MemberRule",
            "MemberPrize",
            "RedemptionRecord",
            "LotteryRecord",
            "LotteryConfig",
            "LotteryItem"
        ]

        var totalDeleted = 0

        for entityName in entityNames {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)

            do {
                let result = try context.execute(deleteRequest) as? NSBatchDeleteResult
                let deletedCount = result?.result as? Int ?? 0
                totalDeleted += deletedCount
                print("✅ 删除 \(entityName): \(deletedCount) 条记录")
            } catch {
                print("❌ 删除 \(entityName) 失败: \(error)")
            }
        }

        // 保存上下文
        do {
            try context.save()
            print("✅ 本地数据清除完成，共删除 \(totalDeleted) 条记录")
            completion(.success(()))
        } catch {
            print("❌ 保存Core Data上下文失败: \(error)")
            completion(.failure(ClearanceError.localDataClearanceFailed))
        }
    }

    /**
     * 步骤4: 删除云端数据
     */
    private func deleteCloudData(userInfo: UserDataInfo, completion: @escaping (Result<Void, Error>) -> Void) {
        print("☁️ 步骤4: 彻底清空iCloud数据...")

        guard userInfo.appleUserID != nil else {
            print("❌ 无法获取Apple用户ID，跳过云端数据清理")
            completion(.success(()))
            return
        }

        // 暂停CloudKit自动同步，避免删除过程中的数据冲突
        print("⏸️ 暂停CloudKit自动同步...")
        pauseCloudKitSync()

        // 批量删除所有CloudKit数据
        clearAllCloudDataBatch { [weak self] success in
            guard let self = self else {
                completion(.failure(ClearanceError.operationCancelled))
                return
            }

            if success {
                print("✅ iCloud数据清空完成")
            } else {
                print("⚠️ iCloud数据清空部分失败，但继续执行")
            }

            // 恢复CloudKit同步
            print("▶️ 恢复CloudKit自动同步...")
            self.resumeCloudKitSync()

            completion(.success(()))
        }
    }

    /**
     * 步骤5: 清除认证信息
     */
    private func clearAuthentication(userInfo: UserDataInfo, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🔐 步骤5: 清除认证信息...")

        // 清除Keychain中的敏感信息
        clearKeychainData()

        // 清除UserDefaults中的用户偏好设置
        clearUserDefaults()

        // 清除iCloud设置同步数据
        clearUbiquitousKeyValueStore()

        print("✅ 认证信息清除完成")
        completion(.success(()))
    }

    /**
     * 步骤6: 最终清理
     */
    private func performFinalCleanup(userInfo: UserDataInfo, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🧹 步骤6: 最终清理...")

        // 清理所有缓存
        clearAllCaches()

        // 清理临时文件
        clearTemporaryFiles()

        // 重置应用状态
        resetApplicationState()

        print("✅ 最终清理完成")
        completion(.success(()))
    }

    // MARK: - Helper Methods

    /**
     * 批量清除所有CloudKit数据
     */
    private func clearAllCloudDataBatch(completion: @escaping (Bool) -> Void) {
        let database = cloudKitContainer.privateCloudDatabase

        // 需要清除的记录类型（对应Core Data实体）
        let recordTypes = [
            "CD_User",
            "CD_Subscription",
            "CD_GlobalRule",
            "CD_Member",
            "CD_PointRecord",
            "CD_DiaryEntry",
            "CD_AIReport",
            "CD_MemberRule",
            "CD_MemberPrize",
            "CD_RedemptionRecord",
            "CD_LotteryRecord",
            "CD_LotteryConfig",
            "CD_LotteryItem"
        ]

        Task {
            var totalDeleted = 0
            var hasErrors = false

            for recordType in recordTypes {
                do {
                    let deletedCount = await deleteAllRecordsOfType(recordType, from: database)
                    totalDeleted += deletedCount
                    print("✅ 删除CloudKit \(recordType): \(deletedCount) 条记录")
                } catch {
                    print("❌ 删除CloudKit \(recordType) 失败: \(error)")
                    hasErrors = true
                }
            }

            print("✅ CloudKit数据清除完成，共删除 \(totalDeleted) 条记录")
            completion(!hasErrors)
        }
    }

    /**
     * 删除指定类型的所有CloudKit记录
     */
    private func deleteAllRecordsOfType(_ recordType: String, from database: CKDatabase) async -> Int {
        var deletedCount = 0
        var cursor: CKQueryOperation.Cursor? = nil

        repeat {
            do {
                let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
                let (matchResults, nextCursor): ([(CKRecord.ID, Result<CKRecord, Error>)], CKQueryOperation.Cursor?)

                if let cursor = cursor {
                    (matchResults, nextCursor) = try await database.records(continuingMatchFrom: cursor)
                } else {
                    (matchResults, nextCursor) = try await database.records(matching: query, resultsLimit: 100)
                }
                cursor = nextCursor

                // 收集要删除的记录ID
                var recordIDsToDelete: [CKRecord.ID] = []
                for (recordID, result) in matchResults {
                    switch result {
                    case .success(_):
                        recordIDsToDelete.append(recordID)
                    case .failure(let error):
                        print("⚠️ 获取记录失败: \(error)")
                    }
                }

                // 批量删除记录
                if !recordIDsToDelete.isEmpty {
                    let (_, deleteResults) = try await database.modifyRecords(saving: [], deleting: recordIDsToDelete)

                    for (recordID, result) in deleteResults {
                        switch result {
                        case .success():
                            deletedCount += 1
                        case .failure(let error):
                            print("⚠️ 删除记录 \(recordID) 失败: \(error)")
                        }
                    }
                }

            } catch {
                print("❌ 查询或删除CloudKit记录失败: \(error)")
                break
            }

        } while cursor != nil

        return deletedCount
    }

    /**
     * 暂停CloudKit同步
     */
    private func pauseCloudKitSync() {
        // 通知同步管理器暂停同步
        NotificationCenter.default.post(name: .pauseCloudKitSync, object: nil)
    }

    /**
     * 恢复CloudKit同步
     */
    private func resumeCloudKitSync() {
        // 通知同步管理器恢复同步
        NotificationCenter.default.post(name: .resumeCloudKitSync, object: nil)
    }

    /**
     * 清除Keychain数据
     */
    private func clearKeychainData() {
        print("🔑 清除Keychain数据...")

        let keychainKeys = [
            "apple_user_id",
            "user_name",
            "user_email",
            "deepseek_api_key"
        ]

        for key in keychainKeys {
            KeychainHelper.delete(key: key)
        }

        print("✅ Keychain数据清除完成")
    }

    /**
     * 清除UserDefaults数据
     */
    private func clearUserDefaults() {
        print("⚙️ 清除UserDefaults数据...")

        let userDefaults = UserDefaults.standard
        let keysToRemove = [
            "user_is_logged_in",
            "apple_user_id",
            "user_name",
            "user_email",
            "last_cloudkit_sync_date",
            "icloud_sync_enabled",
            "last_sync_date",
            "app_language",
            "notifications_enabled",
            "sound_enabled",
            "haptic_feedback_enabled",
            "auto_sync_enabled",
            "GrowthDiaryDraft",
            "subscription_level",
            "first_launch",
            "tutorial_completed"
        ]

        for key in keysToRemove {
            userDefaults.removeObject(forKey: key)
        }

        userDefaults.synchronize()
        print("✅ UserDefaults数据清除完成")
    }

    /**
     * 清除iCloud设置同步数据
     */
    private func clearUbiquitousKeyValueStore() {
        print("☁️ 清除iCloud设置同步数据...")

        let ubiquitousStore = NSUbiquitousKeyValueStore.default
        let keysToRemove = [
            "app_language",
            "notifications_enabled",
            "sound_enabled",
            "haptic_feedback_enabled",
            "auto_sync_enabled",
            "last_sync_timestamp"
        ]

        for key in keysToRemove {
            ubiquitousStore.removeObject(forKey: key)
        }

        ubiquitousStore.synchronize()
        print("✅ iCloud设置同步数据清除完成")
    }

    /**
     * 清除所有缓存
     */
    private func clearAllCaches() {
        print("🗂️ 清除所有缓存...")

        // 清除URL缓存
        URLCache.shared.removeAllCachedResponses()

        print("✅ 缓存清除完成")
    }

    /**
     * 清除临时文件
     */
    private func clearTemporaryFiles() {
        print("📁 清除临时文件...")

        let fileManager = FileManager.default

        // 清除临时目录
        let tempDirectory = NSTemporaryDirectory()
        do {
            let tempContents = try fileManager.contentsOfDirectory(atPath: tempDirectory)
            for file in tempContents {
                let filePath = tempDirectory + file
                try fileManager.removeItem(atPath: filePath)
            }
            print("✅ 临时目录清理完成")
        } catch {
            print("⚠️ 临时目录清理失败: \(error)")
        }

        // 清除应用缓存目录
        if let cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first {
            do {
                let cacheContents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
                for fileURL in cacheContents {
                    try fileManager.removeItem(at: fileURL)
                }
                print("✅ 应用缓存目录清理完成")
            } catch {
                print("⚠️ 应用缓存目录清理失败: \(error)")
            }
        }
    }
}
