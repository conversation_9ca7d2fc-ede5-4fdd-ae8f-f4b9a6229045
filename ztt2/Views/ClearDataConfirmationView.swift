//
//  ClearDataConfirmationView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 数据清除确认视图
 * 
 * 参照ztt1的DeleteAccountConfirmationView实现，提供详细的确认流程：
 * - 显示用户数据统计
 * - 要求输入确认文本
 * - 多重警告提示
 * - 支持取消操作
 */
struct ClearDataConfirmationView: View {
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    @State private var confirmationText = ""
    @State private var userDataStats: [String: Int] = [:]
    @State private var isLoading = false
    @State private var showingProgressView = false
    
    // MARK: - Constants
    private let requiredConfirmationText = "清除所有数据"
    
    // MARK: - Computed Properties
    private var isConfirmationValid: Bool {
        confirmationText.trimmingCharacters(in: .whitespacesAndNewlines) == requiredConfirmationText
    }
    
    private var canProceed: Bool {
        isConfirmationValid && !isLoading
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    headerSection
                    warningSection
                    dataStatisticsSection
                    confirmationSection
                    actionButtonsSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("清除所有数据")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadUserDataStatistics()
        }
        .fullScreenCover(isPresented: $showingProgressView) {
            DataClearanceProgressView()
        }
    }
    
    // MARK: - View Components
    
    /**
     * 头部警告区域
     */
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("⚠️ 危险操作")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.red)
            
            Text("此操作将永久删除您的所有数据，包括本地和云端数据。此操作无法撤销！")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 20)
    }
    
    /**
     * 警告信息区域
     */
    private var warningSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("重要提醒", systemImage: "info.circle.fill")
                .font(.headline)
                .foregroundColor(.orange)
            
            VStack(alignment: .leading, spacing: 8) {
                warningItem("将删除所有家庭成员信息")
                warningItem("将删除所有积分记录和成长日记")
                warningItem("将删除所有AI报告和分析数据")
                warningItem("将清除所有用户设置和偏好")
                warningItem("将影响您的所有已同步设备")
                warningItem("将清除iCloud中的所有同步数据")
            }
        }
        .padding(16)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
    
    /**
     * 警告项目
     */
    private func warningItem(_ text: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "minus.circle.fill")
                .foregroundColor(.orange)
                .font(.caption)
                .padding(.top, 2)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
    
    /**
     * 数据统计区域
     */
    private var dataStatisticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("您的数据统计", systemImage: "chart.bar.fill")
                .font(.headline)
                .foregroundColor(.blue)
            
            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在统计数据...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.vertical, 20)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    dataStatItem("家庭成员", count: userDataStats["members"] ?? 0, icon: "person.2.fill")
                    dataStatItem("积分记录", count: userDataStats["pointRecords"] ?? 0, icon: "star.fill")
                    dataStatItem("成长日记", count: userDataStats["diaryEntries"] ?? 0, icon: "book.fill")
                    dataStatItem("AI报告", count: userDataStats["aiReports"] ?? 0, icon: "brain.head.profile")
                }
            }
        }
        .padding(16)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    /**
     * 数据统计项目
     */
    private func dataStatItem(_ title: String, count: Int, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    /**
     * 确认输入区域
     */
    private var confirmationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("确认操作", systemImage: "checkmark.shield.fill")
                .font(.headline)
                .foregroundColor(.red)
            
            Text("请输入 \"\(requiredConfirmationText)\" 来确认此操作：")
                .font(.body)
                .foregroundColor(.secondary)
            
            TextField("请输入确认文本", text: $confirmationText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            if !confirmationText.isEmpty && !isConfirmationValid {
                Text("确认文本不正确")
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
        .padding(16)
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
    }
    
    /**
     * 操作按钮区域
     */
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            Button(action: proceedWithClearance) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "trash.fill")
                    }
                    
                    Text(isLoading ? "正在处理..." : "确认清除所有数据")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(canProceed ? Color.red : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .disabled(!canProceed)
            
            Button("取消") {
                dismiss()
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color.gray.opacity(0.2))
            .foregroundColor(.primary)
            .cornerRadius(12)
        }
        .padding(.top, 8)
    }
    
    // MARK: - Methods
    
    /**
     * 加载用户数据统计
     */
    private func loadUserDataStatistics() {
        isLoading = true

        Task { @MainActor in
            let stats = DataClearanceManager.shared.getUserDataStatistics()
            self.userDataStats = stats
            self.isLoading = false
        }
    }
    
    /**
     * 执行清除操作
     */
    private func proceedWithClearance() {
        guard canProceed else { return }
        
        // 显示进度视图
        showingProgressView = true
    }
}
