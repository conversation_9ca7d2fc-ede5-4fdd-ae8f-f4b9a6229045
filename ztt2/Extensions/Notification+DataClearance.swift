//
//  Notification+DataClearance.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation

/**
 * 数据清除相关通知扩展
 */
extension Notification.Name {
    
    /// 数据清除完成通知
    static let dataClearanceCompleted = Notification.Name("dataClearanceCompleted")
    
    /// 暂停CloudKit同步通知
    static let pauseCloudKitSync = Notification.Name("pauseCloudKitSync")
    
    /// 恢复CloudKit同步通知
    static let resumeCloudKitSync = Notification.Name("resumeCloudKitSync")
    
    /// 检测到远程清除标记通知
    static let remoteClearanceMarkDetected = Notification.Name("remoteClearanceMarkDetected")
}
